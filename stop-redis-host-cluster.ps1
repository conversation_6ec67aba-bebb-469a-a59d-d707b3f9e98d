# Redis主机网络集群停止脚本
# 专为Windows 11 Docker Desktop开发环境设计
# 安全停止主机网络模式的Redis集群
#
# 功能说明：
# 1. 优雅停止Redis集群节点
# 2. 清理容器和相关资源
# 3. 验证停止状态
# 4. 提供数据保护选项
#
# 与现有环境隔离：
# - 只影响主机网络模式的Redis集群
# - 不影响现有的容器网络模式集群
# - 端口16379/26379/36379与现有7000-7002端口隔离

param(
    [switch]$RemoveData,         # 删除数据文件
    [switch]$Force,              # 强制停止
    [switch]$Verbose,            # 详细输出
    [switch]$KeepContainers     # 保留容器（仅停止）
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success { param([string]$Message) Write-ColorOutput "✅ $Message" "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput "⚠️  $Message" "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput "❌ $Message" "Red" }
function Write-Info { param([string]$Message) Write-ColorOutput "ℹ️  $Message" "Cyan" }

# 脚本开始
Write-ColorOutput "==================== Redis主机网络集群停止脚本 ====================" "Magenta"
Write-Info "停止时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Write-Info "网络模式: 主机网络模式 (network_mode: host)"
Write-Info "目标端口: 16379, 26379, 36379"
Write-ColorOutput "=================================================================" "Magenta"

# 检查Docker是否运行
Write-Info "检查Docker Desktop状态..."
try {
    $dockerInfo = docker info 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Warning "Docker未运行，无需停止容器"
        exit 0
    }
    Write-Success "Docker Desktop运行正常"
} catch {
    Write-Warning "Docker Desktop未运行，无需停止容器"
    exit 0
}

# 检查容器是否存在
Write-Info "检查Redis主机网络集群容器..."
$containers = @("redis-host-node-1", "redis-host-node-2", "redis-host-node-3", "redis-host-cluster-init")
$existingContainers = @()

foreach ($container in $containers) {
    $exists = docker ps -a --filter "name=$container" --format "{{.Names}}" 2>$null
    if ($exists) {
        $existingContainers += $container
    }
}

if ($existingContainers.Count -eq 0) {
    Write-Info "未发现Redis主机网络集群容器，无需停止"
    exit 0
}

Write-Info "发现以下容器: $($existingContainers -join ', ')"

# 数据备份提醒
if (-not $RemoveData) {
    Write-Info "数据保护模式：将保留Redis数据文件"
    Write-Info "数据位置: docker\redis-host\node-*\data\"
} else {
    Write-Warning "数据删除模式：将删除所有Redis数据文件"
    $confirm = Read-Host "确认删除所有数据? 输入 'DELETE' 确认"
    if ($confirm -ne "DELETE") {
        Write-Info "操作已取消"
        exit 0
    }
}

# 优雅停止集群（如果容器正在运行）
Write-Info "检查集群状态并执行优雅停止..."
$runningContainers = docker ps --filter "name=redis-host-node" --format "{{.Names}}" 2>$null

if ($runningContainers) {
    Write-Info "执行集群优雅停止..."
    
    # 尝试保存集群配置
    try {
        Write-Info "保存集群配置..."
        docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p 16379 -a 123456 cluster saveconfig 2>$null
        Write-Success "集群配置已保存"
    } catch {
        Write-Warning "集群配置保存失败（可能集群未初始化）"
    }
    
    # 执行BGSAVE（后台保存数据）
    foreach ($port in @(16379, 26379, 36379)) {
        try {
            Write-Info "保存节点数据 (端口 $port)..."
            docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p $port -a 123456 bgsave 2>$null
            Write-Success "节点 $port 数据保存完成"
        } catch {
            Write-Warning "节点 $port 数据保存失败"
        }
    }
    
    # 等待保存完成
    Write-Info "等待数据保存完成..."
    Start-Sleep -Seconds 3
}

# 停止容器
Write-Info "停止Redis主机网络集群容器..."
try {
    if ($KeepContainers) {
        # 仅停止容器
        if ($Verbose) {
            docker-compose -f docker-compose-host-network.yml stop
        } else {
            docker-compose -f docker-compose-host-network.yml stop 2>$null
        }
        Write-Success "容器已停止（保留容器）"
    } else {
        # 停止并删除容器
        if ($Force) {
            if ($Verbose) {
                docker-compose -f docker-compose-host-network.yml down --remove-orphans
            } else {
                docker-compose -f docker-compose-host-network.yml down --remove-orphans 2>$null
            }
        } else {
            if ($Verbose) {
                docker-compose -f docker-compose-host-network.yml down
            } else {
                docker-compose -f docker-compose-host-network.yml down 2>$null
            }
        }
        Write-Success "容器已停止并删除"
    }
} catch {
    Write-Error "停止容器失败: $_"
    
    # 强制停止单个容器
    Write-Info "尝试强制停止单个容器..."
    foreach ($container in $existingContainers) {
        try {
            docker stop $container 2>$null
            if (-not $KeepContainers) {
                docker rm $container 2>$null
            }
            Write-Success "容器 $container 已强制停止"
        } catch {
            Write-Warning "容器 $container 停止失败"
        }
    }
}

# 验证停止状态
Write-Info "验证停止状态..."
$stillRunning = docker ps --filter "name=redis-host-node" --format "{{.Names}}" 2>$null

if ($stillRunning) {
    Write-Warning "以下容器仍在运行: $stillRunning"
} else {
    Write-Success "所有Redis主机网络集群容器已停止"
}

# 检查端口释放情况
Write-Info "检查端口释放情况..."
$ports = @(16379, 26379, 36379, 18000, 18001, 18002)
$stillOccupied = @()

foreach ($port in $ports) {
    $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($connection) {
        $stillOccupied += $port
    }
}

if ($stillOccupied.Count -gt 0) {
    Write-Warning "以下端口仍被占用: $($stillOccupied -join ', ')"
    Write-Info "可能需要等待几秒钟让端口完全释放"
} else {
    Write-Success "所有端口已释放"
}

# 删除数据文件（如果指定）
if ($RemoveData) {
    Write-Info "删除Redis数据文件..."
    $dataPaths = @(
        "docker\redis-host\node-1\data",
        "docker\redis-host\node-2\data", 
        "docker\redis-host\node-3\data"
    )
    
    foreach ($path in $dataPaths) {
        if (Test-Path $path) {
            try {
                Remove-Item -Path "$path\*" -Recurse -Force 2>$null
                Write-Success "已删除: $path"
            } catch {
                Write-Warning "删除失败: $path - $_"
            }
        }
    }
}

# 清理Docker资源
if ($Force) {
    Write-Info "清理Docker资源..."
    try {
        # 清理未使用的网络
        docker network prune -f 2>$null
        # 清理未使用的卷
        docker volume prune -f 2>$null
        Write-Success "Docker资源清理完成"
    } catch {
        Write-Warning "Docker资源清理失败: $_"
    }
}

# 输出总结信息
Write-ColorOutput "==================== 停止完成 ====================" "Green"
Write-Success "Redis主机网络集群停止完成！"
Write-Info ""
Write-Info "状态总结："
if ($KeepContainers) {
    Write-Info "  ✓ 容器已停止（保留）"
} else {
    Write-Info "  ✓ 容器已停止并删除"
}

if ($RemoveData) {
    Write-Info "  ✓ 数据文件已删除"
} else {
    Write-Info "  ✓ 数据文件已保留"
}

Write-Info "  ✓ 端口已释放: 16379, 26379, 36379"
Write-Info ""
Write-Info "重新启动命令："
Write-Info "  .\start-redis-host-cluster.ps1"
Write-Info ""
Write-Info "如需完全清理："
Write-Info "  .\stop-redis-host-cluster.ps1 -RemoveData -Force"
Write-ColorOutput "====================================================" "Green"
