# Redis主机网络集群状态检查脚本
# 专为Windows 11 Docker Desktop开发环境设计
# 全面检查主机网络模式Redis集群的运行状态
#
# 功能说明：
# 1. 检查容器运行状态
# 2. 验证Redis服务连接
# 3. 检查集群状态和节点信息
# 4. 执行性能测试
# 5. 提供故障排除建议

param(
    [switch]$Detailed,           # 详细检查模式
    [switch]$Performance,        # 性能测试
    [switch]$Continuous,         # 持续监控模式
    [int]$Interval = 5          # 持续监控间隔（秒）
)

# 设置错误处理
$ErrorActionPreference = "Continue"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success { param([string]$Message) Write-ColorOutput "✅ $Message" "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput "⚠️  $Message" "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput "❌ $Message" "Red" }
function Write-Info { param([string]$Message) Write-ColorOutput "ℹ️  $Message" "Cyan" }

# 检查函数
function Test-RedisClusterStatus {
    Write-ColorOutput "==================== Redis主机网络集群状态检查 ====================" "Magenta"
    Write-Info "检查时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    Write-Info "网络模式: 主机网络模式 (network_mode: host)"
    Write-Info "目标端口: 16379, 26379, 36379"
    Write-ColorOutput "=================================================================" "Magenta"

    # 1. 检查Docker状态
    Write-Info "1. 检查Docker Desktop状态..."
    try {
        $dockerInfo = docker info 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Docker Desktop未运行"
            return $false
        }
        Write-Success "Docker Desktop运行正常"
    } catch {
        Write-Error "Docker Desktop检查失败: $_"
        return $false
    }

    # 2. 检查容器状态
    Write-Info "2. 检查容器状态..."
    $containers = @("redis-host-node-1", "redis-host-node-2", "redis-host-node-3")
    $containerStatus = @{}
    $allContainersOk = $true

    foreach ($container in $containers) {
        try {
            $status = docker inspect --format='{{.State.Status}}' $container 2>$null
            $health = docker inspect --format='{{.State.Health.Status}}' $container 2>$null
            
            $containerStatus[$container] = @{
                Status = $status
                Health = $health
            }
            
            if ($status -eq "running") {
                if ($health -eq "healthy" -or $health -eq "") {
                    Write-Success "容器 $container 运行正常"
                } else {
                    Write-Warning "容器 $container 运行但健康检查异常: $health"
                    $allContainersOk = $false
                }
            } else {
                Write-Error "容器 $container 状态异常: $status"
                $allContainersOk = $false
            }
        } catch {
            Write-Error "容器 $container 检查失败: $_"
            $allContainersOk = $false
        }
    }

    # 3. 检查端口连接
    Write-Info "3. 检查Redis端口连接..."
    $ports = @(16379, 26379, 36379)
    $portStatus = @{}
    $allPortsOk = $true

    foreach ($port in $ports) {
        try {
            $connection = Test-NetConnection -ComputerName 127.0.0.1 -Port $port -WarningAction SilentlyContinue
            $portStatus[$port] = $connection.TcpTestSucceeded
            
            if ($connection.TcpTestSucceeded) {
                Write-Success "端口 $port 连接正常"
            } else {
                Write-Error "端口 $port 连接失败"
                $allPortsOk = $false
            }
        } catch {
            Write-Error "端口 $port 检查失败: $_"
            $allPortsOk = $false
        }
    }

    # 4. 检查Redis服务响应
    Write-Info "4. 检查Redis服务响应..."
    $redisStatus = @{}
    $allRedisOk = $true

    foreach ($port in $ports) {
        try {
            $result = docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p $port -a 123456 ping 2>$null
            $redisStatus[$port] = ($result -eq "PONG")
            
            if ($result -eq "PONG") {
                Write-Success "Redis服务 $port 响应正常"
            } else {
                Write-Error "Redis服务 $port 响应异常: $result"
                $allRedisOk = $false
            }
        } catch {
            Write-Error "Redis服务 $port 检查失败: $_"
            $allRedisOk = $false
        }
    }

    # 5. 检查集群状态
    Write-Info "5. 检查Redis集群状态..."
    try {
        $clusterInfo = docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p 16379 -a 123456 cluster info 2>$null
        $clusterNodes = docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p 16379 -a 123456 cluster nodes 2>$null
        
        if ($clusterInfo -match "cluster_state:ok") {
            Write-Success "Redis集群状态正常"
            
            # 解析集群信息
            $clusterSize = ($clusterInfo | Select-String "cluster_size:(\d+)").Matches[0].Groups[1].Value
            $slotsAssigned = ($clusterInfo | Select-String "cluster_slots_assigned:(\d+)").Matches[0].Groups[1].Value
            $knownNodes = ($clusterInfo | Select-String "cluster_known_nodes:(\d+)").Matches[0].Groups[1].Value
            
            Write-Info "  集群大小: $clusterSize"
            Write-Info "  已分配槽位: $slotsAssigned/16384"
            Write-Info "  已知节点: $knownNodes"
            
            if ($Detailed) {
                Write-ColorOutput "==================== 详细集群信息 ====================" "Yellow"
                Write-Host $clusterInfo
                Write-ColorOutput "==================== 集群节点信息 ====================" "Yellow"
                Write-Host $clusterNodes
                Write-ColorOutput "====================================================" "Yellow"
            }
        } else {
            Write-Error "Redis集群状态异常"
            Write-Info "集群信息: $clusterInfo"
            $allRedisOk = $false
        }
    } catch {
        Write-Error "集群状态检查失败: $_"
        $allRedisOk = $false
    }

    # 6. 执行连接测试
    Write-Info "6. 执行集群连接测试..."
    try {
        $testKey = "test_$(Get-Date -Format 'yyyyMMddHHmmss')"
        $testValue = "Redis Host Cluster Test"
        
        # 写入测试
        $setResult = docker exec redis-host-node-1 redis-cli -c -h 127.0.0.1 -p 16379 -a 123456 set $testKey $testValue 2>$null
        
        # 读取测试（从不同节点）
        $getValue = docker exec redis-host-node-1 redis-cli -c -h 127.0.0.1 -p 26379 -a 123456 get $testKey 2>$null
        
        # 清理测试数据
        docker exec redis-host-node-1 redis-cli -c -h 127.0.0.1 -p 36379 -a 123456 del $testKey 2>$null
        
        if ($getValue -eq $testValue) {
            Write-Success "集群读写测试通过"
        } else {
            Write-Error "集群读写测试失败"
            $allRedisOk = $false
        }
    } catch {
        Write-Error "连接测试失败: $_"
        $allRedisOk = $false
    }

    # 7. 性能测试（如果启用）
    if ($Performance) {
        Write-Info "7. 执行性能测试..."
        try {
            Write-Info "执行基准测试（1000次操作）..."
            $benchResult = docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p 16379 -a 123456 --latency-history -i 1 2>$null
            Write-Info "性能测试完成，详细结果请查看容器日志"
        } catch {
            Write-Warning "性能测试失败: $_"
        }
    }

    # 输出总结
    Write-ColorOutput "==================== 检查总结 ====================" "Yellow"
    
    $overallStatus = $allContainersOk -and $allPortsOk -and $allRedisOk
    
    if ($overallStatus) {
        Write-Success "Redis主机网络集群运行正常"
    } else {
        Write-Error "Redis主机网络集群存在问题"
        
        Write-Info "故障排除建议："
        if (-not $allContainersOk) {
            Write-Info "  - 检查容器日志: docker-compose -f docker-compose-host-network.yml logs"
            Write-Info "  - 重启容器: .\start-redis-host-cluster.ps1 -ForceRecreate"
        }
        if (-not $allPortsOk) {
            Write-Info "  - 检查端口占用: netstat -an | findstr '16379 26379 36379'"
            Write-Info "  - 检查防火墙设置"
        }
        if (-not $allRedisOk) {
            Write-Info "  - 重新初始化集群: .\start-redis-host-cluster.ps1 -ForceRecreate"
            Write-Info "  - 检查Redis配置文件"
        }
    }
    
    Write-ColorOutput "====================================================" "Yellow"
    
    return $overallStatus
}

# 主执行逻辑
if ($Continuous) {
    Write-Info "启动持续监控模式，间隔: $Interval 秒"
    Write-Info "按 Ctrl+C 停止监控"
    
    do {
        Clear-Host
        $status = Test-RedisClusterStatus
        
        if ($status) {
            Write-Success "集群状态正常 - $(Get-Date -Format 'HH:mm:ss')"
        } else {
            Write-Error "集群状态异常 - $(Get-Date -Format 'HH:mm:ss')"
        }
        
        Start-Sleep -Seconds $Interval
    } while ($true)
} else {
    # 单次检查
    $status = Test-RedisClusterStatus
    
    if ($status) {
        exit 0
    } else {
        exit 1
    }
}
