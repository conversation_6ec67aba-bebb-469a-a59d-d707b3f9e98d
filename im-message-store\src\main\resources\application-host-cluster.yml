# IM消息存储服务配置 - Redis主机网络集群模式
# 专为Windows 11 Docker Desktop开发环境设计
# 适配主机网络模式的Redis集群，简化网络配置
#
# 网络模式变更说明：
# - Redis集群使用主机网络模式（network_mode: host）
# - 节点通过127.0.0.1直接通信，无需NAT映射
# - 端口配置：16379, 26379, 36379（避免与现有环境冲突）
# - 移除Redisson NAT映射配置，简化连接逻辑

spring:
  # === MySQL 数据源配置 ===
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password: root
    url: ************************************************************************************************************************************************************************
    username: root

    # HikariCP 连接池配置 - 开发环境
    hikari:
      pool-name: IMMessageStoreHostClusterHikariCP
      minimum-idle: 5                    # 开发环境较小的最小空闲连接数
      maximum-pool-size: 20              # 开发环境适中的最大连接池大小
      connection-timeout: 30000          # 连接超时时间（30秒）
      idle-timeout: 600000               # 空闲超时时间（10分钟）
      max-lifetime: 1800000              # 连接最大生命周期（30分钟）
      connection-test-query: SELECT 1    # 连接测试查询
      leak-detection-threshold: 60000    # 连接泄漏检测阈值（60秒）

  # === Redis 主机网络集群配置 (Redisson) ===
  redis:
    # 集群节点配置 - 主机网络模式端口
    cluster:
      nodes: 127.0.0.1:16379,127.0.0.1:26379,127.0.0.1:36379
      max-redirects: 5
    password: 123456
    timeout: 3000

    # Redisson 主机网络模式配置
    redisson:
      # 禁用 NAT 映射（主机网络模式不需要）
      enable-nat-map: false
      
      # 连接池配置 - 开发环境
      connection-pool:
        pool-size: 50                    # 连接池大小
        min-idle: 10                     # 最小空闲连接数
        
      # 超时配置
      connect-timeout: 3000              # 连接超时（毫秒）
      retry-attempts: 3                  # 重试次数
      retry-interval: 1000               # 重试间隔（毫秒）
      
      # 主机网络模式优化配置
      keep-alive: true                   # 启用TCP keep-alive
      tcp-no-delay: true                 # 禁用Nagle算法，减少延迟

  # === RabbitMQ 配置 ===
  rabbitmq:
    # 使用现有的RabbitMQ配置（可以与Redis主机网络模式并存）
    addresses: localhost:5672,localhost:5673,localhost:5674
    username: admin
    password: admin123
    virtual-host: /

    # 开发环境连接配置
    connection-timeout: 30000
    requested-heartbeat: 60

    # 发布者配置
    publisher-confirm-type: correlated
    publisher-returns: true

    # 消费者配置 - 开发环境
    listener:
      simple:
        concurrency: 3                   # 消息存储服务较低并发
        max-concurrency: 8               # 最大并发数
        acknowledge-mode: MANUAL
        prefetch: 5                      # 预取数量
        retry:
          enabled: true
          max-attempts: 3                # 开发环境减少重试次数
          initial-interval: 1000
          multiplier: 2.0
          max-interval: 10000
        default-requeue-rejected: false

    # 模板配置
    template:
      mandatory: true
      receive-timeout: 5000              # 开发环境较短超时
      reply-timeout: 5000
      retry:
        enabled: true
        max-attempts: 3
        initial-interval: 1000

# 开发环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,redis-cluster
  endpoint:
    health:
      show-details: always               # 开发环境显示详细健康信息
  metrics:
    export:
      prometheus:
        enabled: true

# 开发环境性能配置
server:
  port: 8001                             # 消息存储服务端口
  tomcat:
    threads:
      max: 50                            # 开发环境较小线程池
      min-spare: 5
    connection-timeout: 20000
    max-connections: 500                 # 开发环境较小连接数

# 日志配置 - 开发环境
logging:
  level:
    com.lld.im: DEBUG                    # 应用日志级别
    org.redisson: INFO                  # Redisson日志级别
    org.springframework.data.redis: DEBUG # Redis操作日志
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/im-message-host-cluster.log

# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 主机网络模式特殊说明：
# 1. Redis集群节点直接通过127.0.0.1和指定端口通信
# 2. 无需NAT映射配置，简化了连接逻辑
# 3. 端口16379/26379/36379与现有7000/7001/7002端口隔离
# 4. 可以与现有容器网络模式环境并行运行
# 5. 适合开发环境，网络配置更简单直观

# 环境隔离说明：
# - 该配置文件专用于主机网络模式Redis集群
# - 与application-prod.yml（容器网络模式）完全隔离
# - 可以通过spring.profiles.active=host-cluster激活
# - 支持与现有环境并行开发和测试
