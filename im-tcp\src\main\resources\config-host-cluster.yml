# IM-TCP服务配置 - Redis主机网络集群模式
# 专为Windows 11 Docker Desktop开发环境设计
# 适配主机网络模式的Redis集群，简化网络配置
#
# 网络模式变更说明：
# - Redis集群使用主机网络模式（network_mode: host）
# - 节点通过127.0.0.1直接通信，无需NAT映射
# - 端口配置：16379, 26379, 36379（避免与现有环境冲突）
# - 移除复杂的NAT映射配置，简化连接逻辑

lim:
  # TCP服务配置
  tcpPort: 9000
  webSocketPort: 19000
  bossThreadSize: 1
  workThreadSize: 8
  heartBeatTime: 20000                   # 心跳超时时间（毫秒）
  brokerId: 1000
  loginModel: 3                          # 登录模式
  logicUrl: http://127.0.0.1:8000/v1     # 业务逻辑服务URL

  # Redis主机网络集群模式配置
  redis:
    mode: cluster                        # 集群模式
    database: 0
    password: 123456
    timeout: 5000                        # 开发环境超时时间
    poolMinIdle: 8                       # 最小空闲连接数
    poolConnTimeout: 5000                # 连接超时时间（毫秒）
    poolSize: 15                         # 连接池大小
    
    # 主机网络集群配置
    cluster:
      nodes:
        - 127.0.0.1:16379               # redis-host-node-1 (主节点，槽位: 0-5460)
        - 127.0.0.1:26379               # redis-host-node-2 (主节点，槽位: 5461-10922)
        - 127.0.0.1:36379               # redis-host-node-3 (主节点，槽位: 10923-16383)
      maxRedirects: 5                    # 最大重定向次数
      scanInterval: 2000                 # 集群拓扑扫描间隔（毫秒）
      # 主机网络模式连接优化参数
      retryAttempts: 3                   # 重试次数
      retryInterval: 1500                # 重试间隔（毫秒）
      
    # 主机网络模式特殊配置
    hostNetwork:
      enabled: true                      # 启用主机网络模式优化
      directConnection: true             # 直接连接模式
      natMapping: false                  # 禁用NAT映射

  # RabbitMQ配置（可以与Redis主机网络模式并存）
  rabbitmq:
    host: 127.0.0.1
    port: 5672
    username: admin
    password: admin123
    virtualHost: /

# 主机网络模式说明：
# 1. Redis集群节点直接通过127.0.0.1和指定端口通信
# 2. 无需复杂的NAT映射配置，连接更稳定
# 3. 端口16379/26379/36379与现有7000/7001/7002端口完全隔离
# 4. 可以与现有容器网络模式环境并行运行
# 5. 适合开发环境，网络配置更简单直观

# 与现有环境的区别：
# 1. 网络模式：主机网络 vs 容器bridge网络
# 2. 端口范围：16379/26379/36379 vs 7000/7001/7002
# 3. 连接方式：直接IP连接 vs NAT映射连接
# 4. 配置复杂度：更简单 vs 需要复杂的映射配置

# 环境隔离说明：
# - 该配置文件专用于主机网络模式Redis集群
# - 与现有config.yml、config-remote-cluster.yml完全隔离
# - 可以通过指定配置文件路径激活：--spring.config.location=config-host-cluster.yml
# - 支持与现有环境并行开发和测试
