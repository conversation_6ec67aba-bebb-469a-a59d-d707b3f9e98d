# Redis集群主机网络模式配置
# 专为Windows 11 Docker Desktop开发环境设计
# 使用主机网络模式替代容器网络模式，消除DNS解析依赖
# 
# 网络架构变更说明：
# - 从bridge网络模式改为主机网络模式
# - Redis节点直接使用宿主机IP（127.0.0.1）进行通信
# - 避免容器内部主机名解析问题
# - 简化网络配置，提高开发环境的稳定性
#
# 端口分配方案（避免与现有环境冲突）：
# - redis-host-node-1: 16379 (服务端口), 18000 (集群总线端口)
# - redis-host-node-2: 26379 (服务端口), 18001 (集群总线端口)  
# - redis-host-node-3: 36379 (服务端口), 18002 (集群总线端口)
#
# 与现有环境的区别：
# 1. 网络模式：host模式 vs bridge模式
# 2. 端口范围：16379/26379/36379 vs 7000/7001/7002
# 3. 通信方式：直接IP通信 vs 容器DNS解析
# 4. 配置复杂度：更简单 vs 需要NAT映射

version: '3.8'

services:
  # Redis集群节点1 - Windows主机网络模式（端口映射实现）
  redis-host-node-1:
    image: redis:7.0-alpine
    container_name: redis-host-node-1
    # Windows Docker Desktop: 使用端口映射实现主机网络效果
    ports:
      - "16379:16379"
      - "17379:17379"
    volumes:
      - ./docker/redis-host/node-1/data:/data
      - ./docker/redis-host/node-1/conf:/usr/local/etc/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "16379", "-a", "123456", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    environment:
      - REDIS_PORT=16379
      - REDIS_CLUSTER_BUS_PORT=18000
    labels:
      - "redis.cluster.role=master"
      - "redis.cluster.node=1"
      - "redis.network.mode=host"

  # Redis集群节点2 - Windows主机网络模式（端口映射实现）
  redis-host-node-2:
    image: redis:7.0-alpine
    container_name: redis-host-node-2
    # Windows Docker Desktop: 使用端口映射实现主机网络效果
    ports:
      - "26379:26379"
      - "27379:27379"
    volumes:
      - ./docker/redis-host/node-2/data:/data
      - ./docker/redis-host/node-2/conf:/usr/local/etc/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "26379", "-a", "123456", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    environment:
      - REDIS_PORT=26379
      - REDIS_CLUSTER_BUS_PORT=18001
    labels:
      - "redis.cluster.role=master"
      - "redis.cluster.node=2"
      - "redis.network.mode=host"

  # Redis集群节点3 - Windows主机网络模式（端口映射实现）
  redis-host-node-3:
    image: redis:7.0-alpine
    container_name: redis-host-node-3
    # Windows Docker Desktop: 使用端口映射实现主机网络效果
    ports:
      - "36379:36379"
      - "37379:37379"
    volumes:
      - ./docker/redis-host/node-3/data:/data
      - ./docker/redis-host/node-3/conf:/usr/local/etc/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "36379", "-a", "123456", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    environment:
      - REDIS_PORT=36379
      - REDIS_CLUSTER_BUS_PORT=18002
    labels:
      - "redis.cluster.role=master"
      - "redis.cluster.node=3"
      - "redis.network.mode=host"

  # Redis集群初始化服务 - Windows主机网络模式
  redis-host-cluster-init:
    image: redis:7.0-alpine
    container_name: redis-host-cluster-init
    # 不需要网络模式，使用默认bridge网络连接到宿主机
    depends_on:
      redis-host-node-1:
        condition: service_healthy
      redis-host-node-2:
        condition: service_healthy
      redis-host-node-3:
        condition: service_healthy
    command: >
      sh -c "
        echo '等待Redis节点完全启动...' &&
        sleep 15 &&
        echo '开始创建Redis集群（主机网络模式）...' &&
        redis-cli -a 123456 --cluster create
        127.0.0.1:16379
        127.0.0.1:26379
        127.0.0.1:36379
        --cluster-replicas 0
        --cluster-yes &&
        echo '集群创建完成，检查集群状态...' &&
        redis-cli -p 16379 -a 123456 cluster info &&
        redis-cli -p 16379 -a 123456 cluster nodes &&
        echo '✅ Redis主机网络集群初始化完成！' &&
        echo '集群节点信息：' &&
        echo '  Node 1: 127.0.0.1:16379' &&
        echo '  Node 2: 127.0.0.1:26379' &&
        echo '  Node 3: 127.0.0.1:36379' &&
        echo '测试连接命令：' &&
        echo '  redis-cli -c -h 127.0.0.1 -p 16379 -a 123456'
      "
    restart: "no"
    labels:
      - "redis.cluster.init=true"
      - "redis.network.mode=host"

# 主机网络模式说明：
# 1. 不需要定义networks部分，因为使用主机网络
# 2. 不需要端口映射，容器直接使用宿主机端口
# 3. 容器间通过127.0.0.1和指定端口通信
# 4. 简化了网络配置，避免了NAT映射问题

# 数据卷定义（可选，Docker会自动创建）
volumes:
  redis-host-node-1-data:
    driver: local
  redis-host-node-2-data:
    driver: local
  redis-host-node-3-data:
    driver: local

# 注意事项：
# 1. 主机网络模式下，容器与宿主机共享网络命名空间
# 2. 端口直接绑定到宿主机，需要确保端口不冲突
# 3. 防火墙设置可能需要调整以允许端口访问
# 4. 该配置专为开发环境设计，生产环境建议使用bridge网络
