# Redis主机网络集群快速启动指南

## 🚀 5分钟快速启动

### 前置条件
- ✅ Windows 11 系统
- ✅ Docker Desktop 已安装并运行
- ✅ PowerShell 5.0+ 
- ✅ 端口 16379, 26379, 36379 可用

### 一键启动
```powershell
# 1. 启动Redis集群
.\start-redis-host-cluster.ps1

# 2. 验证集群状态
.\check-redis-host-cluster.ps1

# 3. 测试连接
redis-cli -c -h 127.0.0.1 -p 16379 -a 123456
```

## 📋 核心信息速览

### 端口配置
```
Redis服务端口: 16379, 26379, 36379
集群总线端口: 18000, 18001, 18002
密码: 123456
```

### 连接配置
```yaml
# Spring Boot配置
spring:
  redis:
    cluster:
      nodes: 127.0.0.1:16379,127.0.0.1:26379,127.0.0.1:36379
    password: 123456
    redisson:
      enable-nat-map: false
```

### 管理命令
```powershell
# 启动集群
.\start-redis-host-cluster.ps1

# 检查状态  
.\check-redis-host-cluster.ps1

# 停止集群
.\stop-redis-host-cluster.ps1

# 强制重启
.\start-redis-host-cluster.ps1 -ForceRecreate
```

## 🔧 常用操作

### Redis CLI操作
```bash
# 连接集群
redis-cli -c -h 127.0.0.1 -p 16379 -a 123456

# 查看集群信息
redis-cli -h 127.0.0.1 -p 16379 -a 123456 cluster info

# 查看节点信息
redis-cli -h 127.0.0.1 -p 16379 -a 123456 cluster nodes

# 数据操作
redis-cli -c -h 127.0.0.1 -p 16379 -a 123456 set mykey "hello"
redis-cli -c -h 127.0.0.1 -p 26379 -a 123456 get mykey
```

### Spring Boot应用启动
```powershell
# im-service
cd im-service
mvn spring-boot:run -Dspring-boot.run.profiles=host-cluster

# im-message-store
cd im-message-store  
mvn spring-boot:run -Dspring-boot.run.profiles=host-cluster

# im-tcp
cd im-tcp
java -jar target/im-tcp.jar --spring.config.location=src/main/resources/config-host-cluster.yml
```

## ⚠️ 故障排除

### 端口占用
```powershell
# 检查端口
netstat -ano | findstr "16379 26379 36379"

# 结束进程
taskkill /PID <进程ID> /F
```

### 容器问题
```powershell
# 查看日志
docker-compose -f docker-compose-host-network.yml logs

# 重新创建
.\start-redis-host-cluster.ps1 -ForceRecreate
```

### 集群状态异常
```powershell
# 重置集群
docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p 16379 -a 123456 cluster reset hard
docker exec redis-host-node-2 redis-cli -h 127.0.0.1 -p 26379 -a 123456 cluster reset hard  
docker exec redis-host-node-3 redis-cli -h 127.0.0.1 -p 36379 -a 123456 cluster reset hard

# 重新初始化
.\start-redis-host-cluster.ps1 -ForceRecreate
```

## 📊 与现有环境对比

| 特性 | 现有环境 | 主机网络环境 |
|------|----------|-------------|
| 端口 | 7000-7002 | 16379,26379,36379 |
| 网络模式 | bridge | host |
| NAT映射 | 需要 | 不需要 |
| DNS解析 | 容器内部 | 直接IP |
| 配置复杂度 | 高 | 低 |
| 故障排除 | 复杂 | 简单 |

## 🎯 使用场景

### ✅ 推荐使用
- 本地开发环境
- 功能测试和调试
- 快速原型验证
- 学习和实验

### ⚠️ 注意事项
- 仅适用于开发环境
- 与现有环境端口隔离
- 生产环境建议使用容器网络模式

## 📚 详细文档

完整文档请参考: [Redis主机网络集群部署指南.md](docs/Redis主机网络集群部署指南.md)

## 🆘 获取帮助

```powershell
# 查看脚本帮助
Get-Help .\start-redis-host-cluster.ps1 -Full
Get-Help .\check-redis-host-cluster.ps1 -Full
Get-Help .\stop-redis-host-cluster.ps1 -Full

# 详细状态检查
.\check-redis-host-cluster.ps1 -Detailed

# 持续监控
.\check-redis-host-cluster.ps1 -Continuous
```

---

**🎉 恭喜！您已成功部署Redis主机网络集群开发环境！**
