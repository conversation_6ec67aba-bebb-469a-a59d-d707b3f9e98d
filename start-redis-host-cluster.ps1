# Redis主机网络集群启动脚本
# 专为Windows 11 Docker Desktop开发环境设计
# 使用主机网络模式，简化网络配置，避免DNS解析问题
#
# 功能说明：
# 1. 启动3个Redis节点（主机网络模式）
# 2. 自动初始化Redis集群
# 3. 验证集群状态和连接
# 4. 提供详细的状态检查和故障排除信息
#
# 端口分配：
# - Redis服务端口：16379, 26379, 36379
# - 集群总线端口：18000, 18001, 18002
#
# 与现有环境隔离：
# - 使用独立的docker-compose-host-network.yml
# - 端口与现有7000-7002端口不冲突
# - 可以与现有容器网络模式并行运行

param(
    [switch]$SkipPortCheck,      # 跳过端口检查
    [switch]$ForceRecreate,      # 强制重新创建容器
    [switch]$Verbose,            # 详细输出
    [switch]$SkipClusterInit     # 跳过集群初始化
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success { param([string]$Message) Write-ColorOutput "✅ $Message" "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput "⚠️  $Message" "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput "❌ $Message" "Red" }
function Write-Info { param([string]$Message) Write-ColorOutput "ℹ️  $Message" "Cyan" }

# 脚本开始
Write-ColorOutput "==================== Redis主机网络集群启动脚本 ====================" "Magenta"
Write-Info "启动时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Write-Info "网络模式: 主机网络模式 (network_mode: host)"
Write-Info "端口配置: 16379, 26379, 36379 (服务端口)"
Write-Info "集群总线: 18000, 18001, 18002"
Write-ColorOutput "=================================================================" "Magenta"

# 检查Docker是否运行
Write-Info "检查Docker Desktop状态..."
try {
    $dockerInfo = docker info 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker未运行"
    }
    Write-Success "Docker Desktop运行正常"
} catch {
    Write-Error "Docker Desktop未运行，请先启动Docker Desktop"
    exit 1
}

# 检查端口占用情况
if (-not $SkipPortCheck) {
    Write-Info "检查端口占用情况..."
    $ports = @(16379, 26379, 36379, 18000, 18001, 18002)
    $occupiedPorts = @()
    
    foreach ($port in $ports) {
        $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connection) {
            $occupiedPorts += $port
        }
    }
    
    if ($occupiedPorts.Count -gt 0) {
        Write-Warning "以下端口已被占用: $($occupiedPorts -join ', ')"
        Write-Info "如果这些是之前的Redis主机网络集群进程，请先运行停止脚本"
        Write-Info "或者使用 -SkipPortCheck 参数跳过端口检查"
        
        $choice = Read-Host "是否继续启动? (y/N)"
        if ($choice -ne 'y' -and $choice -ne 'Y') {
            Write-Info "启动已取消"
            exit 0
        }
    } else {
        Write-Success "所有端口都可用"
    }
}

# 检查配置文件
Write-Info "检查配置文件..."
$requiredFiles = @(
    "docker-compose-host-network.yml",
    "docker\redis-host\node-1\conf\redis.conf",
    "docker\redis-host\node-2\conf\redis.conf",
    "docker\redis-host\node-3\conf\redis.conf"
)

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Error "配置文件不存在: $file"
        Write-Info "请确保已正确创建所有配置文件"
        exit 1
    }
}
Write-Success "所有配置文件检查通过"

# 停止现有容器（如果存在）
if ($ForceRecreate) {
    Write-Info "强制重新创建容器..."
    docker-compose -f docker-compose-host-network.yml down --remove-orphans 2>$null
    Write-Success "已清理现有容器"
}

# 启动Redis集群
Write-Info "启动Redis主机网络集群..."
try {
    if ($Verbose) {
        docker-compose -f docker-compose-host-network.yml up -d
    } else {
        docker-compose -f docker-compose-host-network.yml up -d 2>$null
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "Docker Compose启动失败"
    }
    Write-Success "Redis集群容器启动成功"
} catch {
    Write-Error "启动失败: $_"
    exit 1
}

# 等待容器启动
Write-Info "等待Redis节点启动..."
Start-Sleep -Seconds 10

# 检查容器状态
Write-Info "检查容器状态..."
$containers = @("redis-host-node-1", "redis-host-node-2", "redis-host-node-3")
$allHealthy = $true

foreach ($container in $containers) {
    $status = docker inspect --format='{{.State.Status}}' $container 2>$null
    if ($status -eq "running") {
        Write-Success "容器 $container 运行正常"
    } else {
        Write-Error "容器 $container 状态异常: $status"
        $allHealthy = $false
    }
}

if (-not $allHealthy) {
    Write-Error "部分容器启动失败，请检查日志"
    Write-Info "查看日志命令: docker-compose -f docker-compose-host-network.yml logs"
    exit 1
}

# 等待Redis服务就绪
Write-Info "等待Redis服务就绪..."
$maxRetries = 30
$retryCount = 0

do {
    $retryCount++
    $allReady = $true
    
    foreach ($port in @(16379, 26379, 36379)) {
        try {
            $result = docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p $port -a 123456 ping 2>$null
            if ($result -ne "PONG") {
                $allReady = $false
                break
            }
        } catch {
            $allReady = $false
            break
        }
    }
    
    if ($allReady) {
        Write-Success "所有Redis节点就绪"
        break
    }
    
    if ($retryCount -ge $maxRetries) {
        Write-Error "Redis节点启动超时"
        exit 1
    }
    
    Write-Info "等待Redis节点就绪... ($retryCount/$maxRetries)"
    Start-Sleep -Seconds 2
} while ($true)

# 集群初始化
if (-not $SkipClusterInit) {
    Write-Info "初始化Redis集群..."
    
    # 检查是否已经是集群模式
    try {
        $clusterInfo = docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p 16379 -a 123456 cluster info 2>$null
        if ($clusterInfo -match "cluster_state:ok") {
            Write-Success "Redis集群已经初始化完成"
        } else {
            # 创建集群
            Write-Info "创建新的Redis集群..."
            $createResult = docker exec redis-host-node-1 redis-cli -a 123456 --cluster create 127.0.0.1:16379 127.0.0.1:26379 127.0.0.1:36379 --cluster-replicas 0 --cluster-yes 2>$null
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Redis集群创建成功"
            } else {
                Write-Error "Redis集群创建失败"
                exit 1
            }
        }
    } catch {
        Write-Error "集群初始化失败: $_"
        exit 1
    }
}

# 验证集群状态
Write-Info "验证集群状态..."
try {
    $clusterInfo = docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p 16379 -a 123456 cluster info
    $clusterNodes = docker exec redis-host-node-1 redis-cli -h 127.0.0.1 -p 16379 -a 123456 cluster nodes
    
    Write-Success "集群信息验证成功"
    Write-ColorOutput "==================== 集群状态信息 ====================" "Yellow"
    Write-Host $clusterInfo
    Write-ColorOutput "==================== 集群节点信息 ====================" "Yellow"
    Write-Host $clusterNodes
    Write-ColorOutput "====================================================" "Yellow"
} catch {
    Write-Error "集群状态验证失败: $_"
}

# 连接测试
Write-Info "执行连接测试..."
try {
    $testResult = docker exec redis-host-node-1 redis-cli -c -h 127.0.0.1 -p 16379 -a 123456 set test_key "Hello Redis Host Cluster!" 2>$null
    $getValue = docker exec redis-host-node-1 redis-cli -c -h 127.0.0.1 -p 26379 -a 123456 get test_key 2>$null
    
    if ($getValue -eq "Hello Redis Host Cluster!") {
        Write-Success "集群连接测试通过"
    } else {
        Write-Warning "集群连接测试异常"
    }
} catch {
    Write-Warning "连接测试失败: $_"
}

# 输出总结信息
Write-ColorOutput "==================== 启动完成 ====================" "Green"
Write-Success "Redis主机网络集群启动成功！"
Write-Info "集群节点信息："
Write-Info "  Node 1: 127.0.0.1:16379 (集群总线: 18000)"
Write-Info "  Node 2: 127.0.0.1:26379 (集群总线: 18001)"
Write-Info "  Node 3: 127.0.0.1:36379 (集群总线: 18002)"
Write-Info ""
Write-Info "测试连接命令："
Write-Info "  redis-cli -c -h 127.0.0.1 -p 16379 -a 123456"
Write-Info ""
Write-Info "Spring Boot配置文件："
Write-Info "  im-service: application-host-cluster.yml"
Write-Info "  im-message-store: application-host-cluster.yml"
Write-Info "  im-tcp: config-host-cluster.yml"
Write-Info ""
Write-Info "管理命令："
Write-Info "  查看日志: docker-compose -f docker-compose-host-network.yml logs"
Write-Info "  停止集群: .\stop-redis-host-cluster.ps1"
Write-Info "  重启集群: .\start-redis-host-cluster.ps1 -ForceRecreate"
Write-ColorOutput "====================================================" "Green"
